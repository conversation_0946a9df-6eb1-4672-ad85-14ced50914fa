{"info": {"_postman_id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "name": "InstaDrive API", "description": "A collection for testing the InstaDrive Car Rental System API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Test Auth Endpoint", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/auth/test", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "auth", "test"]}, "description": "Test if the auth controller is working"}}, {"name": "Register User", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/auth/register", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "auth", "register"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"phone\": \"1234567890\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Register a new user"}}, {"name": "Login User", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "auth", "login"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Login with user credentials"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/auth/admin/login", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "auth", "admin", "login"]}, "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"admin123\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Login with admin credentials"}}], "description": "Authentication endpoints for registering and logging in users"}, {"name": "Cars", "item": [{"name": "Get All Cars", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/cars", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars"]}, "description": "Get a list of all cars"}}, {"name": "Get Car by ID", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/cars/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1"]}, "description": "Get details of a specific car by ID"}}, {"name": "Check Car Availability", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/cars/1/check-availability?startDate=2023-07-01&endDate=2023-07-05", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1", "check-availability"], "query": [{"key": "startDate", "value": "2023-07-01"}, {"key": "endDate", "value": "2023-07-05"}]}, "description": "Check if a car is available for a specific date range"}}, {"name": "Add New Car (Admin)", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/cars", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Toyota\",\n    \"model\": \"Camry\",\n    \"year\": 2022,\n    \"color\": \"Silver\",\n    \"licensePlate\": \"ABC123\",\n    \"pricePerDay\": 50.00,\n    \"transmission\": \"AUTOMATIC\",\n    \"fuelType\": \"PETROL\",\n    \"seats\": 5,\n    \"imageUrl\": \"https://example.com/car-image.jpg\",\n    \"airConditioning\": true,\n    \"bluetooth\": true,\n    \"gpsNavigation\": true,\n    \"leatherSeats\": false,\n    \"sunroof\": false,\n    \"backupCamera\": true,\n    \"parkingSensors\": true,\n    \"keylessEntry\": true,\n    \"heatedSeats\": false,\n    \"appleCarPlay\": true,\n    \"androidAuto\": true\n}", "options": {"raw": {"language": "json"}}}, "description": "Add a new car (requires admin authentication)"}}, {"name": "Update Car (Admin)", "request": {"method": "PUT", "url": {"raw": "http://localhost:8080/api/cars/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Toyota\",\n    \"model\": \"Camry\",\n    \"year\": 2022,\n    \"color\": \"Blue\",\n    \"licensePlate\": \"ABC123\",\n    \"pricePerDay\": 55.00,\n    \"transmission\": \"AUTOMATIC\",\n    \"fuelType\": \"PETROL\",\n    \"seats\": 5,\n    \"imageUrl\": \"https://example.com/car-image.jpg\",\n    \"airConditioning\": true,\n    \"bluetooth\": true,\n    \"gpsNavigation\": true,\n    \"leatherSeats\": false,\n    \"sunroof\": false,\n    \"backupCamera\": true,\n    \"parkingSensors\": true,\n    \"keylessEntry\": true,\n    \"heatedSeats\": false,\n    \"appleCarPlay\": true,\n    \"androidAuto\": true\n}", "options": {"raw": {"language": "json"}}}, "description": "Update an existing car (requires admin authentication)"}}, {"name": "Delete Car (Admin)", "request": {"method": "DELETE", "url": {"raw": "http://localhost:8080/api/cars/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "description": "Delete a car (requires admin authentication)"}}, {"name": "Update Car Features (Admin)", "request": {"method": "PATCH", "url": {"raw": "http://localhost:8080/api/cars/1/features", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1", "features"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"airConditioning\": true,\n    \"bluetooth\": true,\n    \"gpsNavigation\": true,\n    \"leatherSeats\": true,\n    \"sunroof\": false,\n    \"backupCamera\": true,\n    \"parkingSensors\": true,\n    \"keylessEntry\": true,\n    \"heatedSeats\": true,\n    \"appleCarPlay\": true,\n    \"androidAuto\": true\n}", "options": {"raw": {"language": "json"}}}, "description": "Update only the features of a car (requires admin authentication)"}}, {"name": "Update Car Image (Admin)", "request": {"method": "PATCH", "url": {"raw": "http://localhost:8080/api/cars/1/image", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "cars", "1", "image"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"imageUrl\": \"https://example.com/new-car-image.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Update only the image URL of a car (requires admin authentication)"}}, {"name": "Get Car Image", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/car-images/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "car-images", "1"]}, "description": "Get the image for a specific car"}}], "description": "Endpoints for managing cars"}, {"name": "Bookings", "item": [{"name": "Get All Bookings (Admin)", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/bookings", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "bookings"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "description": "Get all bookings (requires admin authentication)"}}, {"name": "Get Booking by ID", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/bookings/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "bookings", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get a specific booking by ID (requires authentication)"}}, {"name": "Create Booking", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/bookings", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "bookings"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"carId\": 1,\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Test User\",\n    \"phone\": \"1234567890\",\n    \"startDate\": \"2023-07-10\",\n    \"endDate\": \"2023-07-15\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Create a new booking (requires authentication)"}}, {"name": "Get My Bookings", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/bookings/my-bookings", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "bookings", "my-bookings"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get bookings for the current user (requires authentication)"}}], "description": "Endpoints for managing bookings"}, {"name": "Payments", "item": [{"name": "UPI Payment", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/payments/upi", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments", "upi"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"bookingId\": 1,\n    \"amount\": 250.00,\n    \"upiId\": \"user@upi\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Process a UPI payment (requires authentication)"}}, {"name": "Card Payment", "request": {"method": "POST", "url": {"raw": "http://localhost:8080/api/payments/card", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments", "card"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"bookingId\": 1,\n    \"amount\": 250.00,\n    \"cardNumber\": \"****************\",\n    \"cardHolderName\": \"Test User\",\n    \"expiryMonth\": 12,\n    \"expiryYear\": 2025,\n    \"cvv\": \"123\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Process a card payment (requires authentication)"}}, {"name": "Get All Payments (Admin)", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/payments", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "description": "Get all payments (requires admin authentication)"}}, {"name": "Get Payment by ID", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/payments/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get a specific payment by ID (requires authentication)"}}, {"name": "Get Current User Payments", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/payments/current-user", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments", "current-user"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get payments for the current user (requires authentication)"}}, {"name": "Get Payments by Booking ID", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/payments/booking/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "payments", "booking", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get payments for a specific booking (requires authentication)"}}], "description": "Endpoints for managing payments"}, {"name": "Users", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "description": "Get all users (requires admin authentication)"}}, {"name": "Get User by ID", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get a specific user by ID (requires authentication)"}}, {"name": "Get Current User Profile", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users/profile", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "profile"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get the current user's profile (requires authentication)"}}, {"name": "Update User", "request": {"method": "PUT", "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated User Name\",\n    \"phone\": \"9876543210\",\n    \"address\": \"123 Main St, City, Country\"\n}", "options": {"raw": {"language": "json"}}}, "description": "Update a user (requires authentication)"}}, {"name": "Delete User (Admin)", "request": {"method": "DELETE", "url": {"raw": "http://localhost:8080/api/users/1", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "1"]}, "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "description": "Delete a user (requires admin authentication)"}}, {"name": "Get My Bookings", "request": {"method": "GET", "url": {"raw": "http://localhost:8080/api/users/my-bookings", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "users", "my-bookings"]}, "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "description": "Get bookings for the current user (requires authentication)"}}], "description": "Endpoints for managing users"}], "variable": [{"key": "user_token", "value": "your_user_jwt_token_here"}, {"key": "admin_token", "value": "your_admin_jwt_token_here"}]}