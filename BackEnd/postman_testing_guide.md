# InstaDrive API Testing Guide with Postman

This guide will help you test the InstaDrive Car Rental System API using Postman.

## Prerequisites

1. [<PERSON>man](https://www.postman.com/downloads/) installed on your computer
2. Java 17 or higher installed
3. MySQL database server running

## Setting Up the Application

1. Make sure your MySQL server is running and the database `instadrive_new` exists
2. Open a terminal/command prompt and navigate to the project root directory
3. Run the application using Maven:
   ```
   mvnw.cmd spring-boot:run
   ```
4. The application will start on port 8080 (http://localhost:8080)

## Importing the Postman Collection

1. Open Postman
2. Click on "Import" in the top left corner
3. Select "File" and browse to the location of the `postman_collection.json` file
4. Click "Import" to add the collection to your Postman workspace

## Using the Collection

The collection is organized into folders for different types of API endpoints:

### Authentication

Start by testing the authentication endpoints:

1. **Test Auth Endpoint**: Verify that the auth controller is working
2. **Register User**: Create a new user account
   - After successful registration, you'll receive a JWT token in the response
   - Copy this token and set it as the value for the `user_token` variable in the collection
3. **Login User**: Login with existing user credentials
   - This will also return a JWT token that you can use for authenticated requests
4. **Admin Login**: Login with admin credentials
   - Copy the admin token and set it as the value for the `admin_token` variable in the collection

### Setting Collection Variables

To set the collection variables:
1. Click on the "InstaDrive API" collection name
2. Go to the "Variables" tab
3. Update the values for `user_token` and `admin_token` with the tokens you received from the login responses
4. Click "Save"

### Testing Other Endpoints

Once you have set up the authentication tokens, you can test the other endpoints:

#### Cars
- Get all cars
- Get a specific car by ID
- Check car availability for a date range
- Add, update, or delete cars (admin only)

#### Bookings
- Get all bookings (admin only)
- Get a specific booking by ID
- Create a new booking
- Get bookings for the current user

#### Payments
- Process UPI payments
- Process card payments
- Get payment information

#### Users
- Get all users (admin only)
- Get user profile information
- Update user information
- Delete users (admin only)
- Get bookings for the current user

## Tips for Testing

1. **Authentication**: Most endpoints require authentication. Make sure you have set the correct token in the collection variables.

2. **Request Bodies**: The collection includes example request bodies for POST, PUT, and PATCH requests. Modify these as needed for your testing.

3. **IDs in URLs**: Many endpoints include an ID in the URL (e.g., `/api/cars/1`). Replace these with actual IDs from your database.

4. **Date Formats**: When testing endpoints that require dates (like car availability), use the format `YYYY-MM-DD`.

5. **Error Handling**: Pay attention to the response status codes and error messages to understand what went wrong if a request fails.

## Common Issues and Solutions

1. **401 Unauthorized**: This usually means your JWT token is missing, expired, or invalid. Try logging in again to get a new token.

2. **403 Forbidden**: This means you don't have permission to access the resource. Some endpoints are admin-only.

3. **404 Not Found**: The resource you're trying to access doesn't exist. Check the ID in your URL.

4. **400 Bad Request**: Your request is malformed. Check your request body for missing or invalid fields.

5. **500 Internal Server Error**: Something went wrong on the server. Check the server logs for more information.

## Testing Workflow Example

Here's a typical workflow for testing the API:

1. Register a new user or login with existing credentials
2. Browse available cars
3. Check availability for a specific car and date range
4. Create a booking for an available car
5. Process a payment for the booking
6. View your bookings using either:
   - The "Get My Bookings" endpoint in the Bookings section (`/api/bookings/my-bookings`)
   - The "Get My Bookings" endpoint in the Users section (`/api/users/my-bookings`)
7. View detailed booking and payment information

Happy testing!
