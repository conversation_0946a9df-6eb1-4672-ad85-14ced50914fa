spring.application.name=InstaDrive
# Database Configuration
spring.datasource.url=******************************************
spring.datasource.username=root
spring.datasource.password=Supriyasql@6505

# Hibernate / JPA
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Server Port
server.port=8080

# Logging
logging.level.org.springframework.web=INFO
logging.level.org.springframework.security=INFO
logging.level.com.alphaweb.instadrive=INFO

# Performance Tuning
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000
spring.jpa.properties.hibernate.jdbc.batch_size=30
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# Removed unused caching configuration

# Spring MVC
spring.mvc.throw-exception-if-no-handler-found=true
spring.web.resources.add-mappings=true

# File Upload Settings
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=15MB

# File Storage Location
file.upload-dir=uploads
file.receipts-dir=receipts

