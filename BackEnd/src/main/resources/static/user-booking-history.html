<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Booking History</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #333;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab-button {
            background-color: #f1f1f1;
            color: #333;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
        }
        .tab-button.active {
            background-color: #4CAF50;
            color: white;
        }
        .booking-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .booking-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.3s;
        }
        .booking-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .booking-id {
            font-weight: bold;
            color: #333;
        }
        .booking-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-confirmed {
            background-color: #4CAF50;
            color: white;
        }
        .status-active {
            background-color: #2196F3;
            color: white;
        }
        .status-upcoming {
            background-color: #9C27B0;
            color: white;
        }
        .status-completed {
            background-color: #9E9E9E;
            color: white;
        }
        .status-cancelled {
            background-color: #F44336;
            color: white;
        }
        .booking-dates {
            margin: 10px 0;
            color: #555;
        }
        .booking-amount {
            font-weight: bold;
            color: #333;
            font-size: 18px;
            margin: 10px 0;
        }
        .booking-reference {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 5px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .car-details {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            color: #4CAF50;
        }
        .stat-label {
            color: #555;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #555;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #757575;
        }
        .empty-state p {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Your Booking History</h1>
        
        <div class="form-group">
            <label for="userId">User ID:</label>
            <input type="number" id="userId" value="1">
            <button onclick="loadUserBookings()" style="margin-top: 10px;">Load Bookings</button>
        </div>
    </div>
    
    <div id="statsContainer" class="container" style="display: none;">
        <h2>Booking Statistics</h2>
        <div class="stats-container" id="statsContent">
            <!-- Stats will be populated here -->
        </div>
    </div>
    
    <div class="container">
        <div class="tab-buttons">
            <button id="allTab" class="tab-button active" onclick="showTab('all')">All Bookings</button>
            <button id="activeTab" class="tab-button" onclick="showTab('active')">Active</button>
            <button id="upcomingTab" class="tab-button" onclick="showTab('upcoming')">Upcoming</button>
            <button id="completedTab" class="tab-button" onclick="showTab('completed')">Completed</button>
            <button id="cancelledTab" class="tab-button" onclick="showTab('cancelled')">Cancelled</button>
        </div>
        
        <div id="bookingsContainer">
            <div class="loading">Loading bookings...</div>
        </div>
    </div>
    
    <div class="container">
        <h2>Create Test Booking</h2>
        <div class="form-group">
            <label for="testUserId">User ID:</label>
            <input type="number" id="testUserId" value="1">
        </div>
        <div class="form-group">
            <label for="testCarId">Car ID:</label>
            <input type="number" id="testCarId" value="1">
        </div>
        <div class="form-group">
            <label for="testStartDate">Start Date:</label>
            <input type="date" id="testStartDate">
        </div>
        <div class="form-group">
            <label for="testEndDate">End Date:</label>
            <input type="date" id="testEndDate">
        </div>
        <div class="form-group">
            <label for="testAmount">Amount:</label>
            <input type="number" id="testAmount" value="150">
        </div>
        <button onclick="createTestBooking()">Create Booking</button>
    </div>
    
    <script>
        // Set default dates for test booking
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            document.getElementById('testStartDate').valueAsDate = today;
            
            const endDate = new Date();
            endDate.setDate(today.getDate() + 3);
            document.getElementById('testEndDate').valueAsDate = endDate;
            
            // Load bookings for default user
            loadUserBookings();
        });
        
        // Global variables to store bookings
        let allBookings = [];
        let currentTab = 'all';
        
        // Load bookings for a user
        async function loadUserBookings() {
            const userId = document.getElementById('userId').value;
            const bookingsContainer = document.getElementById('bookingsContainer');
            
            bookingsContainer.innerHTML = '<div class="loading">Loading bookings...</div>';
            
            try {
                const response = await fetch(`/api/users/${userId}/bookings`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                allBookings = await response.json();
                
                // Load stats
                loadBookingStats(allBookings);
                
                // Show bookings based on current tab
                showTab(currentTab);
                
            } catch (error) {
                bookingsContainer.innerHTML = `
                    <div class="error">
                        Error loading bookings: ${error.message}
                    </div>
                `;
            }
        }
        
        // Show bookings based on tab
        function showTab(tab) {
            currentTab = tab;
            
            // Update active tab button
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            document.getElementById(`${tab}Tab`).classList.add('active');
            
            // Filter bookings based on tab
            let filteredBookings = [];
            
            switch(tab) {
                case 'active':
                    filteredBookings = allBookings.filter(booking => 
                        booking.status === 'ACTIVE' || 
                        (booking.status === 'CONFIRMED' && isDateInRange(booking.startDate, booking.endDate))
                    );
                    break;
                case 'upcoming':
                    filteredBookings = allBookings.filter(booking => 
                        booking.status === 'UPCOMING' || 
                        (booking.status === 'CONFIRMED' && isDateUpcoming(booking.startDate))
                    );
                    break;
                case 'completed':
                    filteredBookings = allBookings.filter(booking => 
                        booking.status === 'COMPLETED' || 
                        (booking.status === 'CONFIRMED' && isDatePast(booking.endDate))
                    );
                    break;
                case 'cancelled':
                    filteredBookings = allBookings.filter(booking => 
                        booking.status === 'CANCELLED'
                    );
                    break;
                default: // 'all'
                    filteredBookings = allBookings;
            }
            
            displayBookings(filteredBookings);
        }
        
        // Display bookings
        function displayBookings(bookings) {
            const bookingsContainer = document.getElementById('bookingsContainer');
            
            if (bookings.length === 0) {
                bookingsContainer.innerHTML = `
                    <div class="empty-state">
                        <p>No bookings found for this category.</p>
                        <button onclick="createTestBooking()">Create a Test Booking</button>
                    </div>
                `;
                return;
            }
            
            const bookingsList = document.createElement('div');
            bookingsList.className = 'booking-list';
            
            bookings.forEach(booking => {
                const bookingCard = document.createElement('div');
                bookingCard.className = 'booking-card';
                
                // Format dates
                const startDate = new Date(booking.startDate).toLocaleDateString();
                const endDate = new Date(booking.endDate).toLocaleDateString();
                
                // Determine status class
                let statusClass = '';
                let status = booking.status || 'CONFIRMED';
                
                switch(status) {
                    case 'CONFIRMED':
                        statusClass = 'status-confirmed';
                        break;
                    case 'ACTIVE':
                        statusClass = 'status-active';
                        break;
                    case 'UPCOMING':
                        statusClass = 'status-upcoming';
                        break;
                    case 'COMPLETED':
                        statusClass = 'status-completed';
                        break;
                    case 'CANCELLED':
                        statusClass = 'status-cancelled';
                        break;
                }
                
                // Create HTML for the booking card
                bookingCard.innerHTML = `
                    <div class="booking-header">
                        <span class="booking-id">Booking #${booking.bookingId || booking.id}</span>
                        <span class="booking-status ${statusClass}">${status}</span>
                    </div>
                    <div class="booking-reference">
                        Ref: ${booking.referenceId || 'N/A'}
                    </div>
                    <div class="booking-dates">
                        ${startDate} to ${endDate}
                    </div>
                    <div class="booking-amount">
                        $${booking.totalAmount.toFixed(2)}
                    </div>
                `;
                
                // Add car details if available
                if (booking.car) {
                    const carDetails = document.createElement('div');
                    carDetails.className = 'car-details';
                    
                    carDetails.innerHTML = `
                        <h3>${booking.car.brand} ${booking.car.model}</h3>
                        <p>Reg: ${booking.car.registrationNumber}</p>
                    `;
                    
                    bookingCard.appendChild(carDetails);
                } else {
                    const carDetails = document.createElement('div');
                    carDetails.className = 'car-details';
                    carDetails.innerHTML = `<p>Car ID: ${booking.carId}</p>`;
                    bookingCard.appendChild(carDetails);
                }
                
                bookingsList.appendChild(bookingCard);
            });
            
            bookingsContainer.innerHTML = '';
            bookingsContainer.appendChild(bookingsList);
        }
        
        // Load booking statistics
        function loadBookingStats(bookings) {
            const statsContainer = document.getElementById('statsContainer');
            const statsContent = document.getElementById('statsContent');
            
            // Calculate stats
            const totalBookings = bookings.length;
            
            const activeBookings = bookings.filter(booking => 
                booking.status === 'ACTIVE' || 
                (booking.status === 'CONFIRMED' && isDateInRange(booking.startDate, booking.endDate))
            ).length;
            
            const upcomingBookings = bookings.filter(booking => 
                booking.status === 'UPCOMING' || 
                (booking.status === 'CONFIRMED' && isDateUpcoming(booking.startDate))
            ).length;
            
            const completedBookings = bookings.filter(booking => 
                booking.status === 'COMPLETED' || 
                (booking.status === 'CONFIRMED' && isDatePast(booking.endDate))
            ).length;
            
            const cancelledBookings = bookings.filter(booking => 
                booking.status === 'CANCELLED'
            ).length;
            
            const totalSpent = bookings
                .filter(booking => booking.status === 'COMPLETED' || (booking.status === 'CONFIRMED' && isDatePast(booking.endDate)))
                .reduce((sum, booking) => sum + booking.totalAmount, 0);
            
            // Create stats cards
            statsContent.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${totalBookings}</div>
                    <div class="stat-label">Total Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${activeBookings}</div>
                    <div class="stat-label">Active Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${upcomingBookings}</div>
                    <div class="stat-label">Upcoming Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${completedBookings}</div>
                    <div class="stat-label">Completed Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${cancelledBookings}</div>
                    <div class="stat-label">Cancelled Bookings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$${totalSpent.toFixed(2)}</div>
                    <div class="stat-label">Total Spent</div>
                </div>
            `;
            
            statsContainer.style.display = 'block';
        }
        
        // Create a test booking
        async function createTestBooking() {
            const userId = document.getElementById('testUserId').value;
            const carId = document.getElementById('testCarId').value;
            const startDate = document.getElementById('testStartDate').value;
            const endDate = document.getElementById('testEndDate').value;
            const amount = document.getElementById('testAmount').value;
            
            const bookingData = {
                userId: parseInt(userId),
                carId: parseInt(carId),
                startDate: startDate,
                endDate: endDate,
                totalAmount: parseFloat(amount)
            };
            
            try {
                const response = await fetch('/api/bookings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(bookingData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const result = await response.json();
                
                alert(`Booking created successfully!\nReference ID: ${result.referenceId}`);
                
                // Reload bookings
                loadUserBookings();
                
            } catch (error) {
                alert(`Error creating booking: ${error.message}`);
            }
        }
        
        // Helper functions for date checking
        function isDateInRange(startDate, endDate) {
            const today = new Date().toISOString().split('T')[0];
            return startDate <= today && endDate >= today;
        }
        
        function isDateUpcoming(startDate) {
            const today = new Date().toISOString().split('T')[0];
            return startDate > today;
        }
        
        function isDatePast(endDate) {
            const today = new Date().toISOString().split('T')[0];
            return endDate < today;
        }
    </script>
</body>
</html>
