# InstaDrive - Car Rental System

## Car Images Setup

The application now uses local car images instead of placeholder URLs. To complete the setup:

1. Add the following car images to the `public/images/cars/` directory:
   - toyota-camry.jpg
   - honda-civic.jpg
   - bmw-3.jpg
   - toyota-rav4.jpg
   - honda-crv.jpg
   - bmw-x5.jpg
   - mercedes-s.jpg
   - audi-a8.jpg
   - porsche-911.jpg
   - ferrari-f8.jpg
   - default-car.jpg

2. Image requirements:
   - Format: JPG/JPEG
   - Aspect ratio: 16:9 recommended
   - Minimum width: 800px
   - File size: Optimize for web (< 200KB per image recommended)

3. The application will automatically use these images based on car brand and model.

## Image Handling

The application uses the `imageUtils.js` utility to handle car images:
- `getCarImageUrl(car)` - Returns the appropriate image URL for a car
- `getAllCarImagePaths()` - Returns a list of all car image paths

If a car has a specific `imageUrl` property, that will be used. Otherwise, the system will generate a URL based on the car's brand and model.