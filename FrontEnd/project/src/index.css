@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  @apply bg-gray-50;
}

h1, h2, h3, h4, h5, h6 {
  @apply font-heading font-semibold leading-tight;
}

h1 {
  @apply text-3xl md:text-4xl;
}

h2 {
  @apply text-2xl md:text-3xl;
}

h3 {
  @apply text-xl md:text-2xl;
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-accent {
    @apply btn bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500;
  }
  
  .btn-outline {
    @apply btn border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-outline-sm {
    @apply btn-outline px-3 py-1 text-sm;
  }

  .btn-danger-sm {
    @apply px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-card p-6 transition-transform duration-300 hover:shadow-lg;
  }

  .shadow-card {
    @apply shadow-md;
  }
  
  .form-group {
    @apply flex flex-col space-y-2 mb-4;
  }
  
  .label {
    @apply text-sm font-medium text-gray-700;
  }
  
  .container {
    @apply px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl;
  }
}