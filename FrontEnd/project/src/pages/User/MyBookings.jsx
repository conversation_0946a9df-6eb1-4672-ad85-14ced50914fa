import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FaCalendarAlt, FaCar, FaMapMarkerAlt, FaEye, FaTimes, FaExclamationTriangle } from 'react-icons/fa'
import { motion } from 'framer-motion'
import { toast } from 'react-toastify'
import { getCurrentAndFutureBookings, cancelBooking } from '../../services/bookingService'
import PageHeader from '../../components/UI/PageHeader'
import Spinner from '../../components/UI/Spinner'

const MyBookings = () => {
  const [bookings, setBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [cancellingBooking, setCancellingBooking] = useState(null)

  useEffect(() => {
    fetchBookings()
  }, [])

  const fetchBookings = async () => {
    setLoading(true)
    try {
      const data = await getCurrentAndFutureBookings()

      // Check if data is an array
      if (Array.isArray(data)) {
        setBookings(data)
      } else {
        console.error('Unexpected response format:', data)
        setBookings([])
        toast.error('Received invalid data format from server')
      }
    } catch (error) {
      console.error('Error fetching bookings:', error)
      setBookings([])
      toast.error('Failed to load your bookings. Please check your connection and try again.')
    } finally {
      setLoading(false)
    }
  }



  const handleCancelBooking = async (bookingId) => {
    if (cancellingBooking) return

    if (!window.confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
      return
    }

    setCancellingBooking(bookingId)

    try {
      await cancelBooking(bookingId)
      toast.success('Booking cancelled successfully')
      fetchBookings() // Refresh the bookings list
    } catch (error) {
      console.error('Error cancelling booking:', error)
      toast.error('Failed to cancel booking. Please check your connection and try again.')
    } finally {
      setCancellingBooking(null)
    }
  }

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' }
    return new Date(dateString).toLocaleDateString(undefined, options)
  }

  return (
    <div>
      <PageHeader
        title="My Bookings"
        subtitle="View and manage your current and upcoming car rentals"
        image="https://images.pexels.com/photos/3786091/pexels-photo-3786091.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
      />

      <div className="container mx-auto px-4 py-8">
        {loading ? (
          <div className="flex justify-center py-12">
            <Spinner size="large" />
          </div>
        ) : bookings.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-xl shadow-card p-8">
            <FaCalendarAlt className="text-5xl text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold mb-2">No Bookings Found</h2>
            <p className="text-gray-600 mb-6">You don't have any current or upcoming bookings.</p>
            <Link to="/user/dashboard" className="btn-primary">
              Browse Cars
            </Link>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-2xl font-semibold mb-6">Your Bookings</h2>

            <div className="space-y-6">
              {bookings.map(booking => (
                <motion.div
                  key={booking.bookingId || booking.id}
                  className="bg-white rounded-xl shadow-card overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="p-6">
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
                      <div>
                        <h3 className="text-xl font-semibold">
                          {booking.car?.brand} {booking.car?.model} {booking.car?.year || ''}
                        </h3>
                        <p className="text-gray-600">
                          {booking.referenceId || booking.bookingReference || `BOOK-${(booking.bookingId || booking.id).toString().padStart(6, '0')}`} • {booking.status}
                        </p>
                      </div>

                      <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
                        <Link
                          to={`/user/booking/${booking.bookingId || booking.id}`}
                          className="btn-outline-sm flex items-center"
                        >
                          <FaEye className="mr-1" />
                          Details
                        </Link>

                        {booking.status === 'CONFIRMED' && (
                          <button
                            className="btn-danger-sm flex items-center"
                            onClick={() => handleCancelBooking(booking.bookingId || booking.id)}
                            disabled={cancellingBooking === (booking.bookingId || booking.id)}
                          >
                            {cancellingBooking === (booking.bookingId || booking.id) ? (
                              <span className="animate-spin mr-1">⏳</span>
                            ) : (
                              <FaTimes className="mr-1" />
                            )}
                            Cancel
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-10 h-10 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center">
                          <FaCar />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-gray-600">Car</p>
                          <p className="font-medium">
                            {booking.car?.brand} {booking.car?.model} {booking.car?.year || ''}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-10 h-10 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center">
                          <FaCalendarAlt />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-gray-600">Rental Period</p>
                          <p className="font-medium">
                            {formatDate(booking.startDate)} - {formatDate(booking.endDate)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <div className="flex-shrink-0 w-10 h-10 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center">
                          <FaMapMarkerAlt />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-gray-600">Total Amount</p>
                          <p className="font-medium">${booking.totalAmount || 'N/A'}</p>
                        </div>
                      </div>
                    </div>

                    {booking.status === 'PENDING' && (
                      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center">
                        <FaExclamationTriangle className="text-yellow-500 mr-2" />
                        <p className="text-sm text-yellow-700">
                          Your booking is pending confirmation. We'll notify you once it's confirmed.
                        </p>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default MyBookings
