import { useState, useEffect } from 'react'
import { useParams, useLocation, useNavigate } from 'react-router-dom'
import { FaCreditCard, FaLock, FaCalendarAlt, FaUser, FaMobileAlt, FaQrcode } from 'react-icons/fa'
import { toast } from 'react-toastify'
import { motion } from 'framer-motion'
import { processCardPayment, processUpiPayment } from '../../services/paymentService'
import PageHeader from '../../components/UI/PageHeader'
import Spinner from '../../components/UI/Spinner'

const Payment = () => {
  const { bookingId } = useParams()
  const location = useLocation()
  const navigate = useNavigate()

  // Get info from location state, or use defaults
  const { totalPrice = 0, car = null } = location.state || {}

  const [paymentMethod, setPaymentMethod] = useState('card') // 'card' or 'upi'

  const [paymentInfo, setPaymentInfo] = useState({
    // Card payment fields
    cardName: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',

    // UPI payment fields
    upiId: '',
    upiMobile: ''
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})

  const handleInputChange = (e) => {
    const { name, value } = e.target

    let formattedValue = value

    // Format card number with spaces
    if (name === 'cardNumber') {
      formattedValue = value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim().slice(0, 19)
    }

    // Format expiry date with slash
    if (name === 'expiryDate') {
      formattedValue = value
        .replace(/\D/g, '')
        .replace(/^(\d{2})(\d)/, '$1/$2')
        .slice(0, 5)
    }

    // Format CVV to max 3 or 4 digits
    if (name === 'cvv') {
      formattedValue = value.replace(/\D/g, '').slice(0, 4)
    }

    // Format UPI ID
    if (name === 'upiId') {
      // Allow only alphanumeric characters, @, and .
      formattedValue = value.replace(/[^a-zA-Z0-9@.]/g, '')
    }

    // Format mobile number
    if (name === 'upiMobile') {
      // Allow only numbers and limit to 10 digits
      formattedValue = value.replace(/\D/g, '').slice(0, 10)
    }

    setPaymentInfo(prev => ({ ...prev, [name]: formattedValue }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Handle payment method change
  const handlePaymentMethodChange = (method) => {
    setPaymentMethod(method)
    // Clear errors when switching payment methods
    setErrors({})
  }

  const validateForm = () => {
    const newErrors = {}

    if (paymentMethod === 'card') {
      // Validate card payment fields
      if (!paymentInfo.cardName.trim()) {
        newErrors.cardName = 'Name on card is required'
      }

      if (!paymentInfo.cardNumber.trim()) {
        newErrors.cardNumber = 'Card number is required'
      } else if (paymentInfo.cardNumber.replace(/\s/g, '').length < 16) {
        newErrors.cardNumber = 'Card number must be 16 digits'
      }

      if (!paymentInfo.expiryDate.trim()) {
        newErrors.expiryDate = 'Expiry date is required'
      } else if (!/^\d{2}\/\d{2}$/.test(paymentInfo.expiryDate)) {
        newErrors.expiryDate = 'Expiry date must be in MM/YY format'
      }

      if (!paymentInfo.cvv.trim()) {
        newErrors.cvv = 'CVV is required'
      } else if (paymentInfo.cvv.length < 3) {
        newErrors.cvv = 'CVV must be 3 or 4 digits'
      }
    } else if (paymentMethod === 'upi') {
      // Validate UPI payment fields
      if (!paymentInfo.upiId.trim()) {
        newErrors.upiId = 'UPI ID is required'
      } else if (!paymentInfo.upiId.includes('@')) {
        newErrors.upiId = 'Please enter a valid UPI ID (e.g., name@upi)'
      }

      if (!paymentInfo.upiMobile.trim()) {
        newErrors.upiMobile = 'Mobile number is required'
      } else if (paymentInfo.upiMobile.length !== 10) {
        newErrors.upiMobile = 'Mobile number must be 10 digits'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) return

    setLoading(true)

    try {
      let response;

      if (paymentMethod === 'card') {
        // Prepare card payment data
        const cardPaymentData = {
          bookingId,
          paymentMethod: 'card',
          amount: totalPrice,
          cardDetails: {
            // In a real app, you'd use a secure payment processor
            // and NOT send raw card details
            cardNumber: paymentInfo.cardNumber.replace(/\s/g, ''),
            cardName: paymentInfo.cardName,
            expiryDate: paymentInfo.expiryDate,
            cvv: paymentInfo.cvv
          }
        }

        // Process the card payment
        response = await processCardPayment(cardPaymentData)
      } else {
        // Prepare UPI payment data
        const upiPaymentData = {
          bookingId,
          paymentMethod: 'upi',
          amount: totalPrice,
          upiDetails: {
            upiId: paymentInfo.upiId,
            mobileNumber: paymentInfo.upiMobile
          }
        }

        // Process the UPI payment
        response = await processUpiPayment(upiPaymentData)
      }

      // Use transaction ID from response or generate one
      const transactionId = response?.transactionId ||
        `TXN${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`

      // Use payment date from response or generate one
      const paymentDate = response?.paymentDate || new Date().toISOString()

      // Generate a booking reference number if bookingId is not available
      const bookingReference = bookingId ||
        (response?.bookingId ? response.bookingId : Math.floor(Math.random() * 1000000).toString())

      toast.success(`Payment successful via ${paymentMethod.toUpperCase()}!`)
      navigate(`/user/booking-success/${bookingReference}`, {
        state: {
          bookingId: bookingReference,
          totalPrice,
          car,
          paymentMethod,
          transactionId,
          paymentDate
        }
      })
    } catch (error) {
      console.error('Payment error:', error)
      toast.error(`${paymentMethod.toUpperCase()} payment processing failed. Please try again.`)
    } finally {
      setLoading(false)
    }
  }

  // If navigated directly without required info, show error
  if (!bookingId) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h2 className="text-3xl font-bold mb-4">Payment Information Missing</h2>
        <p className="mb-6">Please complete your booking before proceeding to payment.</p>
        <button
          onClick={() => navigate('/user/dashboard')}
          className="btn-primary"
        >
          Browse Cars
        </button>
      </div>
    )
  }

  return (
    <div>
      <PageHeader
        title="Secure Payment"
        subtitle="Complete your booking with our secure payment system"
      />

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <motion.div
            className="bg-white rounded-xl shadow-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold">Payment Details</h2>
                <div className="flex items-center text-primary-600">
                  <FaLock className="mr-1" />
                  <span className="text-sm">Secure Payment</span>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              <div className="mb-6">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <button
                    type="button"
                    onClick={() => handlePaymentMethodChange('card')}
                    className={`flex items-center rounded-lg p-3 transition duration-200 ${
                      paymentMethod === 'card'
                        ? 'bg-primary-100 border-2 border-primary-600'
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <div className={`w-12 h-8 rounded-md flex items-center justify-center ${
                      paymentMethod === 'card' ? 'bg-primary-600' : 'bg-gray-400'
                    }`}>
                      <FaCreditCard className="text-white" />
                    </div>
                    <span className="ml-2 text-sm font-medium">Credit/Debit Card</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => handlePaymentMethodChange('upi')}
                    className={`flex items-center rounded-lg p-3 transition duration-200 ${
                      paymentMethod === 'upi'
                        ? 'bg-primary-100 border-2 border-primary-600'
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <div className={`w-12 h-8 rounded-md flex items-center justify-center ${
                      paymentMethod === 'upi' ? 'bg-primary-600' : 'bg-gray-400'
                    }`}>
                      <FaQrcode className="text-white" />
                    </div>
                    <span className="ml-2 text-sm font-medium">UPI Payment</span>
                  </button>
                </div>

                {/* Card Payment Form */}
                {paymentMethod === 'card' && (
                  <>
                    <div className="form-group">
                      <label htmlFor="cardName" className="label flex items-center">
                        <FaUser className="mr-2 text-primary-600" />
                        Name on Card
                      </label>
                      <input
                        type="text"
                        id="cardName"
                        name="cardName"
                        value={paymentInfo.cardName}
                        onChange={handleInputChange}
                        className={`input ${errors.cardName ? 'border-red-500' : ''}`}
                        placeholder="John Doe"
                        disabled={loading}
                      />
                      {errors.cardName && <p className="text-red-500 text-sm mt-1">{errors.cardName}</p>}
                    </div>

                    <div className="form-group">
                      <label htmlFor="cardNumber" className="label flex items-center">
                        <FaCreditCard className="mr-2 text-primary-600" />
                        Card Number
                      </label>
                      <input
                        type="text"
                        id="cardNumber"
                        name="cardNumber"
                        value={paymentInfo.cardNumber}
                        onChange={handleInputChange}
                        className={`input ${errors.cardNumber ? 'border-red-500' : ''}`}
                        placeholder="1234 5678 9012 3456"
                        disabled={loading}
                      />
                      {errors.cardNumber && <p className="text-red-500 text-sm mt-1">{errors.cardNumber}</p>}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="form-group">
                        <label htmlFor="expiryDate" className="label flex items-center">
                          <FaCalendarAlt className="mr-2 text-primary-600" />
                          Expiry Date
                        </label>
                        <input
                          type="text"
                          id="expiryDate"
                          name="expiryDate"
                          value={paymentInfo.expiryDate}
                          onChange={handleInputChange}
                          className={`input ${errors.expiryDate ? 'border-red-500' : ''}`}
                          placeholder="MM/YY"
                          disabled={loading}
                        />
                        {errors.expiryDate && <p className="text-red-500 text-sm mt-1">{errors.expiryDate}</p>}
                      </div>

                      <div className="form-group">
                        <label htmlFor="cvv" className="label flex items-center">
                          <FaLock className="mr-2 text-primary-600" />
                          CVV
                        </label>
                        <input
                          type="text"
                          id="cvv"
                          name="cvv"
                          value={paymentInfo.cvv}
                          onChange={handleInputChange}
                          className={`input ${errors.cvv ? 'border-red-500' : ''}`}
                          placeholder="123"
                          disabled={loading}
                        />
                        {errors.cvv && <p className="text-red-500 text-sm mt-1">{errors.cvv}</p>}
                      </div>
                    </div>

                    {/* Test Card Note */}
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <h3 className="text-sm font-medium text-gray-700 mb-2">For Testing: Use these card details</h3>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <p className="text-gray-600">Card Number:</p>
                          <p className="font-mono">4242 4242 4242 4242</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Expiry & CVV:</p>
                          <p className="font-mono">12/30 - 123</p>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* UPI Payment Form */}
                {paymentMethod === 'upi' && (
                  <>
                    <div className="form-group">
                      <label htmlFor="upiId" className="label flex items-center">
                        <FaQrcode className="mr-2 text-primary-600" />
                        UPI ID
                      </label>
                      <input
                        type="text"
                        id="upiId"
                        name="upiId"
                        value={paymentInfo.upiId}
                        onChange={handleInputChange}
                        className={`input ${errors.upiId ? 'border-red-500' : ''}`}
                        placeholder="yourname@upi"
                        disabled={loading}
                      />
                      {errors.upiId && <p className="text-red-500 text-sm mt-1">{errors.upiId}</p>}
                    </div>

                    <div className="form-group">
                      <label htmlFor="upiMobile" className="label flex items-center">
                        <FaMobileAlt className="mr-2 text-primary-600" />
                        Mobile Number
                      </label>
                      <input
                        type="text"
                        id="upiMobile"
                        name="upiMobile"
                        value={paymentInfo.upiMobile}
                        onChange={handleInputChange}
                        className={`input ${errors.upiMobile ? 'border-red-500' : ''}`}
                        placeholder="10-digit mobile number"
                        disabled={loading}
                      />
                      {errors.upiMobile && <p className="text-red-500 text-sm mt-1">{errors.upiMobile}</p>}
                    </div>

                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center mb-2">
                        <FaQrcode className="text-primary-600 mr-2" />
                        <h3 className="text-sm font-medium text-gray-700">How UPI Payment Works</h3>
                      </div>
                      <ol className="text-sm text-gray-600 list-decimal pl-4 space-y-1">
                        <li>Enter your UPI ID and mobile number</li>
                        <li>Click "Pay Now" to initiate payment</li>
                        <li>You'll receive a payment request on your UPI app</li>
                        <li>Approve the payment in your UPI app</li>
                      </ol>

                      <div className="mt-4 p-3 bg-primary-50 rounded border border-primary-100">
                        <p className="text-sm text-primary-700">
                          <strong>For Testing:</strong> Use UPI ID "test@upi" and any 10-digit mobile number
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>

              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total Amount</span>
                  <span className="text-primary-600">${totalPrice}</span>
                </div>
              </div>

              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div className="flex items-center text-gray-600 text-sm">
                  <FaLock className="mr-2" />
                  <span>
                    {paymentMethod === 'card'
                      ? 'All card information is securely encrypted'
                      : 'Your UPI transaction is secure and protected'}
                  </span>
                </div>

                <button
                  type="submit"
                  className="btn-primary w-full md:w-auto px-8"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Spinner size="small" className="mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      {paymentMethod === 'card' ? <FaCreditCard className="mr-2" /> : <FaQrcode className="mr-2" />}
                      Pay ${totalPrice} with {paymentMethod === 'card' ? 'Card' : 'UPI'}
                    </>
                  )}
                </button>
              </div>


            </form>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default Payment