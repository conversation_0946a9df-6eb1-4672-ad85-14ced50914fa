/**
 * Sample car data for development and fallback purposes
 */

const sampleCars = [
  {
    id: 1,
    name: "Toyota Camry 2024",
    brand: "Toyota",
    model: "Camry",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Silver",
    seats: 5,
    pricePerDay: 45,
    price_per_day: 45,
    registrationNumber: "TY-2024-001",
    description: "Comfortable and reliable sedan perfect for city driving and long trips.",
    imageUrl: "/images/cars/toyota-camry.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: false,
      sunroof: false,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: false,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 2,
    name: "Honda Civic 2024",
    brand: "Honda",
    model: "Civic",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Manual",
    color: "Blue",
    seats: 5,
    pricePerDay: 40,
    price_per_day: 40,
    registrationNumber: "HD-2024-002",
    description: "Sporty and efficient compact car with excellent fuel economy.",
    imageUrl: "/images/cars/honda-civic.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: false,
      leatherSeats: false,
      sunroof: true,
      backupCamera: true,
      parkingSensors: false,
      keylessEntry: true,
      heatedSeats: false,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 3,
    name: "BMW 3 Series 2024",
    brand: "BMW",
    model: "3 Series",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Black",
    seats: 5,
    pricePerDay: 75,
    price_per_day: 75,
    registrationNumber: "BM-2024-003",
    description: "Luxury sedan with premium features and exceptional performance.",
    imageUrl: "/images/cars/bmw-3.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: true,
      sunroof: true,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: true,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 4,
    name: "Toyota RAV4 2024",
    brand: "Toyota",
    model: "RAV4",
    year: 2024,
    fuelType: "Hybrid",
    transmission: "Automatic",
    color: "White",
    seats: 7,
    pricePerDay: 55,
    price_per_day: 55,
    registrationNumber: "TY-2024-004",
    description: "Spacious SUV perfect for family trips and outdoor adventures.",
    imageUrl: "/images/cars/toyota-rav4.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: false,
      sunroof: false,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: false,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 5,
    name: "Honda CR-V 2024",
    brand: "Honda",
    model: "CR-V",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Red",
    seats: 5,
    pricePerDay: 50,
    price_per_day: 50,
    registrationNumber: "HD-2024-005",
    description: "Reliable and versatile SUV with excellent safety ratings.",
    imageUrl: "/images/cars/honda-crv.jpg",
    status: "rented",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: false,
      sunroof: true,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: false,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 6,
    name: "BMW X5 2024",
    brand: "BMW",
    model: "X5",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Gray",
    seats: 7,
    pricePerDay: 95,
    price_per_day: 95,
    registrationNumber: "BM-2024-006",
    description: "Premium luxury SUV with advanced technology and comfort features.",
    imageUrl: "/images/cars/bmw-x5.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: true,
      sunroof: true,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: true,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 7,
    name: "Mercedes S-Class 2024",
    brand: "Mercedes",
    model: "S-Class",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Black",
    seats: 5,
    pricePerDay: 120,
    price_per_day: 120,
    registrationNumber: "MB-2024-007",
    description: "Ultimate luxury sedan with cutting-edge technology and comfort.",
    imageUrl: "/images/cars/mercedes-s.jpg",
    status: "available",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: true,
      sunroof: true,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: true,
      appleCarPlay: true,
      androidAuto: true
    }
  },
  {
    id: 8,
    name: "Audi A8 2024",
    brand: "Audi",
    model: "A8",
    year: 2024,
    fuelType: "Petrol",
    transmission: "Automatic",
    color: "Silver",
    seats: 5,
    pricePerDay: 110,
    price_per_day: 110,
    registrationNumber: "AD-2024-008",
    description: "Sophisticated luxury sedan with innovative features and elegant design.",
    imageUrl: "/images/cars/audi-a8.jpg",
    status: "maintenance",
    features: {
      airConditioning: true,
      bluetooth: true,
      gpsNavigation: true,
      leatherSeats: true,
      sunroof: true,
      backupCamera: true,
      parkingSensors: true,
      keylessEntry: true,
      heatedSeats: true,
      appleCarPlay: true,
      androidAuto: true
    }
  }
];

export default sampleCars;
