import api from './api'

// Payment endpoints
export const processUpiPayment = async (paymentData) => {
  try {
    if (!paymentData.bookingId) {
      throw new Error('Booking ID is required for UPI payment')
    }

    const response = await api.post('/payments/upi', paymentData)
    return response.data
  } catch (error) {
    console.error('UPI payment error:', error)

    // For development, return a mock payment response
    if (import.meta.env.DEV) {
      console.warn('Returning mock UPI payment response for development')
      return {
        success: true,
        transactionId: `UPI${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
        paymentDate: new Date().toISOString(),
        amount: paymentData.amount || 0,
        message: 'Payment processed successfully'
      }
    }

    throw error
  }
}

export const processCardPayment = async (paymentData) => {
  try {
    if (!paymentData.bookingId) {
      throw new Error('Booking ID is required for card payment')
    }

    const response = await api.post('/payments/card', paymentData)
    return response.data
  } catch (error) {
    console.error('Card payment error:', error)

    // For development, return a mock payment response
    if (import.meta.env.DEV) {
      console.warn('Returning mock card payment response for development')
      return {
        success: true,
        transactionId: `CARD${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
        paymentDate: new Date().toISOString(),
        amount: paymentData.amount || 0,
        message: 'Payment processed successfully'
      }
    }

    throw error
  }
}

export const confirmPaymentTransaction = async (transactionId) => {
  try {
    const response = await api.get(`/payments/confirm/${transactionId}`)
    return response.data
  } catch (error) {
    console.error(`Error confirming payment transaction ${transactionId}:`, error)
    throw error
  }
}

export const getPaymentHistory = async () => {
  try {
    const response = await api.get('/payments/history')
    return response.data
  } catch (error) {
    console.error('Error fetching payment history:', error)
    throw error
  }
}

// Helper functions for payment flow
export const processPayment = async (paymentData) => {
  try {
    // Validate inputs
    if (!paymentData.bookingId) {
      throw new Error('Booking ID is required for payment')
    }

    if (!paymentData.paymentMethod) {
      throw new Error('Payment method is required')
    }

    // Process payment based on method
    if (paymentData.paymentMethod.toLowerCase() === 'upi') {
      return processUpiPayment(paymentData)
    } else if (paymentData.paymentMethod.toLowerCase() === 'card') {
      return processCardPayment(paymentData)
    } else {
      throw new Error(`Unsupported payment method: ${paymentData.paymentMethod}`)
    }
  } catch (error) {
    console.error(`Error processing payment for booking ${paymentData.bookingId}:`, error)
    throw error
  }
}